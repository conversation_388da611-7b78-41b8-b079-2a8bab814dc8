import { Request, Response } from "express"
import User, { IUser } from "../models/User"

export const createUser = async (req: Request, res: Response) => {
  try {
    const { fullName, email, phoneNumber, dateOfBirth, address } = req.body

    // Validate required fields
    if (!fullName || !email || !phoneNumber || !dateOfBirth || !address) {
      return res.status(400).json({
        success: false,
        message:
          "All fields are required: fullName, email, phoneNumber, dateOfBirth, address",
      })
    }

    // Check if user with email already exists
    const existingUser = await User.findOne({ email: email.toLowerCase() })
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: "User with this email already exists",
      })
    }

    // Create new user
    const newUser: IUser = new User({
      fullName,
      email,
      phoneNumber,
      dateOfBirth: new Date(dateOfBirth),
      address,
    })

    const savedUser = await newUser.save()

    res.status(201).json({
      success: true,
      message: "User created successfully",
      data: savedUser,
    })
  } catch (error) {
    console.error("Error creating user:", error)

    // Handle mongoose validation errors
    if (error instanceof Error && error.name === "ValidationError") {
      return res.status(400).json({
        success: false,
        message: "Validation error",
        error: error.message,
      })
    }

    // Handle duplicate key error (email uniqueness)
    if (error instanceof Error && "code" in error && error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: "User with this email already exists",
      })
    }

    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    })
  }
}

export const getUsers = async (req: Request, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1
    const limit = 10 // 10 users per page
    const search = (req.query.search as string) || ""

    // Calculate skip value for pagination
    const skip = (page - 1) * limit

    // Build search query
    let searchQuery = {}
    if (search.trim()) {
      searchQuery = {
        fullName: { $regex: search.trim(), $options: "i" }, // Case-insensitive search
      }
    }

    // Get total count for pagination
    const totalUsers = await User.countDocuments(searchQuery)
    const totalPages = Math.ceil(totalUsers / limit)

    // Get users with pagination and search
    const users = await User.find(searchQuery)
      .select("fullName email phoneNumber dateOfBirth address createdAt")
      .sort({ createdAt: -1 }) // Sort by newest first
      .skip(skip)
      .limit(limit)

    res.status(200).json({
      success: true,
      data: {
        users,
        totalPages,
        currentPage: page,
        totalUsers,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      },
    })
  } catch (error) {
    console.error("Error fetching users:", error)
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    })
  }
}
