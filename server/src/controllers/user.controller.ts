import { Request, Response } from "express"
import { faker } from "@faker-js/faker"
import User, { IUser } from "../models/User"

export const createUser = async (req: Request, res: Response) => {
  try {
    const { fullName, email, phoneNumber, dateOfBirth, address } = req.body

    // Validate required fields
    if (!fullName || !email || !phoneNumber || !dateOfBirth || !address) {
      return res.status(400).json({
        success: false,
        message:
          "All fields are required: fullName, email, phoneNumber, dateOfBirth, address",
      })
    }

    // Check if user with email already exists
    const existingUser = await User.findOne({ email: email.toLowerCase() })
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: "User with this email already exists",
      })
    }

    // Create new user
    const newUser: IUser = new User({
      fullName,
      email,
      phoneNumber,
      dateOfBirth: new Date(dateOfBirth),
      address,
    })

    const savedUser = await newUser.save()

    res.status(201).json({
      success: true,
      message: "User created successfully",
      data: savedUser,
    })
  } catch (error) {
    console.error("Error creating user:", error)

    // Handle mongoose validation errors
    if (error instanceof Error && error.name === "ValidationError") {
      return res.status(400).json({
        success: false,
        message: "Validation error",
        error: error.message,
      })
    }

    // Handle duplicate key error (email uniqueness)
    if (error instanceof Error && "code" in error && error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: "User with this email already exists",
      })
    }

    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    })
  }
}

export const getUsers = async (req: Request, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1
    const limit = 10 // 10 users per page
    const search = (req.query.search as string) || ""

    // Calculate skip value for pagination
    const skip = (page - 1) * limit

    // Build search query
    let searchQuery = {}
    if (search.trim()) {
      searchQuery = {
        fullName: { $regex: search.trim(), $options: "i" }, // Case-insensitive search
      }
    }

    // Get total count for pagination
    const totalUsers = await User.countDocuments(searchQuery)
    const totalPages = Math.ceil(totalUsers / limit)

    // Get users with pagination and search
    const users = await User.find(searchQuery)
      .select("fullName email phoneNumber dateOfBirth address createdAt")
      .sort({ createdAt: -1 }) // Sort by newest first
      .skip(skip)
      .limit(limit)

    res.status(200).json({
      success: true,
      data: {
        users,
        totalPages,
        currentPage: page,
        totalUsers,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      },
    })
  } catch (error) {
    console.error("Error fetching users:", error)
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    })
  }
}

export const getUserById = async (req: Request, res: Response) => {
  try {
    const { id } = req.params

    // Validate MongoDB ObjectId format
    if (!id.match(/^[0-9a-fA-F]{24}$/)) {
      return res.status(400).json({
        success: false,
        message: "Invalid user ID format",
      })
    }

    // Find user by ID
    const user = await User.findById(id)

    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      })
    }

    // Generate mock data using faker
    const mockData = {
      jobTitle: faker.person.jobTitle(),
      company: faker.company.name(),
      location: `${faker.location.city()}, ${faker.location.country()}`,
      bio: faker.person.bio(),
      socialMedia: {
        linkedin: `https://linkedin.com/in/${faker.internet.username()}`,
        twitter: `https://twitter.com/${faker.internet.username()}`,
        github: `https://github.com/${faker.internet.username()}`,
        website: faker.internet.url(),
      },
      avatar: faker.image.avatar(),
      skills: Array.from({ length: faker.number.int({ min: 3, max: 8 }) }, () =>
        faker.hacker.noun()
      ),
      experience: `${faker.number.int({ min: 1, max: 15 })} years`,
    }

    // Combine real user data with mock data
    const enrichedUser = {
      ...user.toObject(),
      ...mockData,
    }

    res.status(200).json({
      success: true,
      data: enrichedUser,
    })
  } catch (error) {
    console.error("Error fetching user by ID:", error)
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    })
  }
}
