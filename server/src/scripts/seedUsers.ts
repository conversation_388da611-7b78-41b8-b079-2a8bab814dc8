import { faker } from "@faker-js/faker"
import mongoose from "mongoose"
import User, { IUser } from "../models/User"
import { connectDB } from "../config/db"

// Function to generate a Pakistani phone number
const generatePakistaniPhone = (): string => {
  // Generate 10 random digits for Pakistani phone number
  const digits = faker.string.numeric(8)
  return `+9203${digits}`
}

// Function to generate a realistic Pakistani address
const generatePakistaniAddress = (): string => {
  const cities = [
    "Karachi",
    "Lahore",
    "Islamabad",
    "Rawalpindi",
    "Faisalabad",
    "Multan",
    "Peshawar",
    "Quetta",
    "Sialkot",
    "Gujranwala",
    "Hyderabad",
    "Bahawalpur",
    "Sargodha",
    "Sukkur",
    "Larkana",
  ]

  const areas = [
    "Block A",
    "Block B",
    "Block C",
    "Block D",
    "Block E",
    "Sector 1",
    "Sector 2",
    "Sector 3",
    "Phase 1",
    "Phase 2",
    "Model Town",
    "Garden Town",
    "Gulberg",
    "DHA",
    "Clifton",
  ]

  const streetNumber = faker.number.int({ min: 1, max: 999 })
  const area = faker.helpers.arrayElement(areas)
  const city = faker.helpers.arrayElement(cities)

  return `House ${streetNumber}, ${area}, ${city}, Pakistan`
}

// Function to generate a date of birth ensuring user is at least 12 years old
const generateDateOfBirth = (): Date => {
  const today = new Date()
  const maxAge = 80 // Maximum age
  const minAge = 12 // Minimum age as per validation

  // Calculate date range
  const maxDate = new Date(
    today.getFullYear() - minAge,
    today.getMonth(),
    today.getDate()
  )
  const minDate = new Date(
    today.getFullYear() - maxAge,
    today.getMonth(),
    today.getDate()
  )

  return faker.date.between({ from: minDate, to: maxDate })
}

// Function to generate a single user
const generateUser = (): Partial<IUser> => {
  const firstName = faker.person.firstName()
  const lastName = faker.person.lastName()

  return {
    fullName: `${firstName} ${lastName}`,
    email: faker.internet.email({ firstName, lastName }).toLowerCase(),
    phoneNumber: generatePakistaniPhone(),
    dateOfBirth: generateDateOfBirth(),
    address: generatePakistaniAddress(),
  }
}

// Main seeding function
const seedUsers = async (): Promise<void> => {
  try {
    console.log("🌱 Starting user seeding process...")

    // Connect to database
    await connectDB()
    console.log("✅ Connected to database")

    // Clear existing users (optional - comment out if you want to keep existing users)
    // await User.deleteMany({})
    // console.log('🗑️  Cleared existing users')

    const users: Partial<IUser>[] = []
    const batchSize = 10 // Process users in batches
    let successCount = 0
    let errorCount = 0

    console.log("📝 Generating 100 random users...")

    // Generate users in batches to handle potential duplicate emails
    for (let i = 0; i < 100; i += batchSize) {
      const batch: Partial<IUser>[] = []

      // Generate batch of users
      for (let j = 0; j < batchSize && i + j < 100; j++) {
        batch.push(generateUser())
      }

      // Insert batch
      try {
        const insertedUsers = await User.insertMany(batch, { ordered: false })
        successCount += insertedUsers.length
        console.log(
          `✅ Batch ${Math.floor(i / batchSize) + 1}: Inserted ${
            insertedUsers.length
          } users`
        )
      } catch (error: any) {
        // Handle duplicate key errors and other validation errors
        if (error.writeErrors) {
          const successful = batchSize - error.writeErrors.length
          successCount += successful
          errorCount += error.writeErrors.length

          console.log(
            `⚠️  Batch ${
              Math.floor(i / batchSize) + 1
            }: ${successful} successful, ${
              error.writeErrors.length
            } failed (likely duplicates)`
          )

          // Log specific errors for debugging
          error.writeErrors.forEach((writeError: any, index: number) => {
            if (writeError.code === 11000) {
              console.log(
                `   - Duplicate email: ${batch[writeError.index]?.email}`
              )
            } else {
              console.log(`   - Validation error: ${writeError.errmsg}`)
            }
          })
        } else {
          console.error(
            `❌ Batch ${Math.floor(i / batchSize) + 1} failed:`,
            error.message
          )
          errorCount += batchSize
        }
      }
    }

    console.log("\n📊 Seeding Summary:")
    console.log(`✅ Successfully created: ${successCount} users`)
    console.log(`❌ Failed to create: ${errorCount} users`)
    console.log(`📈 Success rate: ${((successCount / 100) * 100).toFixed(1)}%`)

    // Display some sample users
    const sampleUsers = await User.find()
      .limit(5)
      .select("fullName email phoneNumber dateOfBirth address")
    console.log("\n👥 Sample users created:")
    sampleUsers.forEach((user, index) => {
      console.log(
        `${index + 1}. ${user.fullName} (${user.email}) - ${user.phoneNumber}`
      )
    })

    console.log("\n🎉 User seeding completed successfully!")
  } catch (error) {
    console.error("❌ Error during seeding:", error)
    process.exit(1)
  } finally {
    // Close database connection
    await mongoose.connection.close()
    console.log("🔌 Database connection closed")
    process.exit(0)
  }
}

// Run the seeding function
if (require.main === module) {
  seedUsers()
}

export default seedUsers
