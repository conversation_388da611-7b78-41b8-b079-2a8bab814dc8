import express from "express"
import cors from "cors"
import { connectDB } from "./config/db"
import userRoutes from "./routes/user.routes"

const app = express()
const PORT = process.env.PORT || 3000

// Connect to MongoDB
connectDB()

app.use(cors())
app.use(express.json())

// Routes
app.use("/users", userRoutes)

app.listen(PORT, () => {
  console.log(`Server running at http://localhost:${PORT}`)
})
