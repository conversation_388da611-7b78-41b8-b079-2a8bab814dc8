import { Router } from "express"
import {
  createUser,
  getUsers,
  getUserById,
} from "../controllers/user.controller"

const router = Router()

// GET /api/users - Get all users with pagination and search
router.get("/", getUsers)

// GET /api/users/:id - Get a specific user by ID with mock data
router.get("/:id", getUserById)

// POST /api/users - Create a new user
router.post("/", createUser)

export default router
