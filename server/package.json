{"name": "server", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "tsx watch --env-file=.env src/index.ts", "seed:users": "tsx --env-file=.env src/scripts/seedUsers.ts"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@faker-js/faker": "^9.8.0", "cors": "^2.8.5", "express": "^5.1.0", "mongoose": "^8.15.1"}, "devDependencies": {"@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@types/node": "^22.15.29", "tsx": "^4.19.4", "typescript": "^5.8.3"}}