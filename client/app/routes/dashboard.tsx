import { useState, useEffect } from "react"
import {
  useLoaderData,
  useSearchParams,
  useNavigate,
  Link,
  type LoaderFunctionArgs,
} from "react-router"
import type { Route } from "./+types/dashboard"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "~/components/ui/card"
import { Input } from "~/components/ui/input"
import { Button } from "~/components/ui/button"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table"
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "~/components/ui/pagination"

// Types
interface User {
  _id: string
  fullName: string
  email: string
  phoneNumber: string
  dateOfBirth: string
  address: string
  createdAt: string
}

interface UsersResponse {
  success: boolean
  data: {
    users: User[]
    totalPages: number
    currentPage: number
    totalUsers: number
    hasNextPage: boolean
    hasPrevPage: boolean
  }
}

// Loader function
export async function loader({ request }: LoaderFunctionArgs) {
  const url = new URL(request.url)
  const page = url.searchParams.get("page") || "1"
  const search = url.searchParams.get("search") || ""

  try {
    const apiUrl = new URL(
      `/api/users?page=${page}${
        search ? `&search=${encodeURIComponent(search)}` : ""
      }`,
      url
    )
    const response = await fetch(apiUrl)

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data: UsersResponse = await response.json()
    return data
  } catch (error) {
    console.error("Error fetching users:", error)
    throw new Response("Failed to load users", { status: 500 })
  }
}

// Meta function for SEO and page metadata
export function meta({ data }: Route.MetaArgs) {
  const totalUsers = data?.data?.totalUsers || 0
  const currentPage = data?.data?.currentPage || 1

  return [
    { title: `User Dashboard - Page ${currentPage} | MERN Stack Task` },
    {
      name: "description",
      content: `View and manage all registered users. Currently showing ${totalUsers} total users with search and pagination functionality.`,
    },
    {
      name: "keywords",
      content:
        "user dashboard, user management, pagination, search users, MERN stack",
    },
    {
      property: "og:title",
      content: `User Dashboard - ${totalUsers} Users | MERN Stack Task`,
    },
    {
      property: "og:description",
      content: `Browse through ${totalUsers} registered users with advanced search and pagination features.`,
    },
    { property: "og:type", content: "website" },
    { name: "robots", content: "noindex, nofollow" }, // Since this is a demo app
  ]
}

export default function Dashboard() {
  const data = useLoaderData<UsersResponse>()

  const [searchParams, setSearchParams] = useSearchParams()
  const navigate = useNavigate()
  const [searchInput, setSearchInput] = useState(
    searchParams.get("search") || ""
  )

  const { users, totalPages, currentPage, totalUsers } = data.data

  // Handle search with debouncing
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      const newSearchParams = new URLSearchParams(searchParams)
      if (searchInput.trim()) {
        newSearchParams.set("search", searchInput.trim())
      } else {
        newSearchParams.delete("search")
      }
      newSearchParams.set("page", "1") // Reset to first page when searching
      setSearchParams(newSearchParams)
    }, 500) // 500ms debounce

    return () => clearTimeout(timeoutId)
  }, [searchInput])

  // Handle page change
  const handlePageChange = (page: number) => {
    const newSearchParams = new URLSearchParams(searchParams)
    newSearchParams.set("page", page.toString())
    setSearchParams(newSearchParams)
  }

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  // Generate pagination items
  const generatePaginationItems = () => {
    const items = []
    const maxVisiblePages = 5

    if (totalPages <= maxVisiblePages) {
      // Show all pages if total pages is small
      for (let i = 1; i <= totalPages; i++) {
        items.push(
          <PaginationItem key={i}>
            <PaginationLink
              onClick={() => handlePageChange(i)}
              isActive={currentPage === i}
              className="cursor-pointer"
            >
              {i}
            </PaginationLink>
          </PaginationItem>
        )
      }
    } else {
      // Show first page
      items.push(
        <PaginationItem key={1}>
          <PaginationLink
            onClick={() => handlePageChange(1)}
            isActive={currentPage === 1}
            className="cursor-pointer"
          >
            1
          </PaginationLink>
        </PaginationItem>
      )

      // Show ellipsis if needed
      if (currentPage > 3) {
        items.push(
          <PaginationItem key="ellipsis1">
            <PaginationEllipsis />
          </PaginationItem>
        )
      }

      // Show pages around current page
      const start = Math.max(2, currentPage - 1)
      const end = Math.min(totalPages - 1, currentPage + 1)

      for (let i = start; i <= end; i++) {
        items.push(
          <PaginationItem key={i}>
            <PaginationLink
              onClick={() => handlePageChange(i)}
              isActive={currentPage === i}
              className="cursor-pointer"
            >
              {i}
            </PaginationLink>
          </PaginationItem>
        )
      }

      // Show ellipsis if needed
      if (currentPage < totalPages - 2) {
        items.push(
          <PaginationItem key="ellipsis2">
            <PaginationEllipsis />
          </PaginationItem>
        )
      }

      // Show last page
      if (totalPages > 1) {
        items.push(
          <PaginationItem key={totalPages}>
            <PaginationLink
              onClick={() => handlePageChange(totalPages)}
              isActive={currentPage === totalPages}
              className="cursor-pointer"
            >
              {totalPages}
            </PaginationLink>
          </PaginationItem>
        )
      }
    }

    return items
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-7xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl font-bold">User Dashboard</CardTitle>
            <CardDescription>
              Manage and view all registered users. Total users: {totalUsers}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* Search Input */}
            <div className="mb-6">
              <Input
                placeholder="Search users by full name..."
                value={searchInput}
                onChange={(e) => setSearchInput(e.target.value)}
                className="max-w-md"
              />
            </div>

            {/* Users Table */}
            {users.length > 0 ? (
              <div className="space-y-4">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Full Name</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Phone Number</TableHead>
                      <TableHead>Date of Birth</TableHead>
                      <TableHead>Address</TableHead>
                      <TableHead>Registered</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {users.map((user) => (
                      <TableRow key={user._id}>
                        <TableCell className="font-medium">
                          {user.fullName}
                        </TableCell>
                        <TableCell>{user.email}</TableCell>
                        <TableCell>{user.phoneNumber}</TableCell>
                        <TableCell>{formatDate(user.dateOfBirth)}</TableCell>
                        <TableCell
                          className="max-w-xs truncate"
                          title={user.address}
                        >
                          {user.address}
                        </TableCell>
                        <TableCell>{formatDate(user.createdAt)}</TableCell>
                        <TableCell>
                          <Button asChild variant="outline" size="sm">
                            <Link to={`/user/${user._id}`}>View Profile</Link>
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="flex justify-center">
                    <Pagination>
                      <PaginationContent>
                        <PaginationItem>
                          <PaginationPrevious
                            onClick={() =>
                              handlePageChange(Math.max(1, currentPage - 1))
                            }
                            className={`cursor-pointer ${
                              currentPage === 1
                                ? "pointer-events-none opacity-50"
                                : ""
                            }`}
                          />
                        </PaginationItem>

                        {generatePaginationItems()}

                        <PaginationItem>
                          <PaginationNext
                            onClick={() =>
                              handlePageChange(
                                Math.min(totalPages, currentPage + 1)
                              )
                            }
                            className={`cursor-pointer ${
                              currentPage === totalPages
                                ? "pointer-events-none opacity-50"
                                : ""
                            }`}
                          />
                        </PaginationItem>
                      </PaginationContent>
                    </Pagination>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-500 text-lg">
                  {searchInput.trim()
                    ? "No users found matching your search."
                    : "No users registered yet."}
                </p>
                <Button onClick={() => navigate("/register")} className="mt-4">
                  Register First User
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
