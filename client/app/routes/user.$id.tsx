import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, type LoaderFunctionArgs } from "react-router"
import type { Route } from "./+types/user.$id"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "~/components/ui/card"
import { But<PERSON> } from "~/components/ui/button"
import { Badge } from "~/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar"
import {
  MapPin,
  Briefcase,
  Building,
  Calendar,
  Mail,
  Phone,
  Home,
  ExternalLink,
  ArrowLeft,
  User,
  Clock,
} from "lucide-react"

// Types
interface UserProfile {
  _id: string
  fullName: string
  email: string
  phoneNumber: string
  dateOfBirth: string
  address: string
  createdAt: string
  updatedAt: string
  // Mock data fields
  jobTitle: string
  company: string
  location: string
  bio: string
  socialMedia: {
    linkedin: string
    twitter: string
    github: string
    website: string
  }
  avatar: string
  skills: string[]
  experience: string
}

interface UserResponse {
  success: boolean
  data: UserProfile
}

// Loader function
export async function loader({ params, request }: LoaderFunctionArgs) {
  const { id } = params
  const url = new URL(request.url)

  if (!id) {
    throw new Response("User ID is required", { status: 400 })
  }

  try {
    const apiUrl = new URL(`/api/users/${id}`, url)
    const response = await fetch(apiUrl)

    if (!response.ok) {
      if (response.status === 404) {
        throw new Response("User not found", { status: 404 })
      }
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data: UserResponse = await response.json()
    return data
  } catch (error) {
    console.error("Error fetching user:", error)
    if (error instanceof Response) {
      throw error
    }
    throw new Response("Failed to load user", { status: 500 })
  }
}

// Meta function for SEO
export function meta({ data, params }: Route.MetaArgs) {
  const user = data?.data
  const userName = user?.fullName || "User"

  return [
    { title: `${userName} - User Profile | MERN Stack Task` },
    {
      name: "description",
      content: `View detailed profile information for ${userName} including contact details, professional information, and social media links.`,
    },
    {
      name: "keywords",
      content: `user profile, ${userName}, contact information, professional details`,
    },
    {
      property: "og:title",
      content: `${userName} - User Profile`,
    },
    {
      property: "og:description",
      content: `Professional profile for ${userName} with contact details and social media links.`,
    },
    { property: "og:type", content: "profile" },
    { name: "robots", content: "noindex, nofollow" },
  ]
}

// Helper function to format dates
const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  })
}

// Helper function to get initials
const getInitials = (name: string): string => {
  return name
    .split(" ")
    .map((n) => n[0])
    .join("")
    .toUpperCase()
    .slice(0, 2)
}

export default function UserProfile() {
  const data = useLoaderData<UserResponse>()
  const user = data.data

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header with back button */}
        <div className="mb-6">
          <Button asChild variant="outline" className="mb-4">
            <Link to="/dashboard">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Dashboard
            </Link>
          </Button>
        </div>

        {/* Main Profile Card */}
        <Card className="mb-6">
          <CardHeader>
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
              <Avatar className="w-20 h-20">
                <AvatarImage src={user.avatar} alt={user.fullName} />
                <AvatarFallback className="text-lg">
                  {getInitials(user.fullName)}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <CardTitle className="text-2xl font-bold">
                  {user.fullName}
                </CardTitle>
                <CardDescription className="text-lg mt-1">
                  {user.jobTitle} at {user.company}
                </CardDescription>
                <div className="flex items-center gap-2 mt-2 text-muted-foreground">
                  <MapPin className="w-4 h-4" />
                  <span>{user.location}</span>
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-gray-700 leading-relaxed">{user.bio}</p>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Contact Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="w-5 h-5" />
                Contact Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-3">
                <Mail className="w-4 h-4 text-muted-foreground" />
                <div>
                  <p className="text-sm text-muted-foreground">Email</p>
                  <p className="font-medium">{user.email}</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Phone className="w-4 h-4 text-muted-foreground" />
                <div>
                  <p className="text-sm text-muted-foreground">Phone</p>
                  <p className="font-medium">{user.phoneNumber}</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Home className="w-4 h-4 text-muted-foreground" />
                <div>
                  <p className="text-sm text-muted-foreground">Address</p>
                  <p className="font-medium">{user.address}</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Calendar className="w-4 h-4 text-muted-foreground" />
                <div>
                  <p className="text-sm text-muted-foreground">Date of Birth</p>
                  <p className="font-medium">{formatDate(user.dateOfBirth)}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Professional Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Briefcase className="w-5 h-5" />
                Professional Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-3">
                <Building className="w-4 h-4 text-muted-foreground" />
                <div>
                  <p className="text-sm text-muted-foreground">Company</p>
                  <p className="font-medium">{user.company}</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Briefcase className="w-4 h-4 text-muted-foreground" />
                <div>
                  <p className="text-sm text-muted-foreground">Job Title</p>
                  <p className="font-medium">{user.jobTitle}</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Clock className="w-4 h-4 text-muted-foreground" />
                <div>
                  <p className="text-sm text-muted-foreground">Experience</p>
                  <p className="font-medium">{user.experience}</p>
                </div>
              </div>
              <div>
                <p className="text-sm text-muted-foreground mb-2">Skills</p>
                <div className="flex flex-wrap gap-2">
                  {user.skills.map((skill, index) => (
                    <Badge key={index} variant="secondary">
                      {skill}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Social Media Links */}
          <Card>
            <CardHeader>
              <CardTitle>Social Media & Links</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <a
                href={user.socialMedia.linkedin}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div className="w-8 h-8 bg-blue-600 rounded flex items-center justify-center">
                  <span className="text-white text-xs font-bold">in</span>
                </div>
                <span className="font-medium">LinkedIn</span>
                <ExternalLink className="w-4 h-4 ml-auto text-muted-foreground" />
              </a>
              <a
                href={user.socialMedia.twitter}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div className="w-8 h-8 bg-sky-500 rounded flex items-center justify-center">
                  <span className="text-white text-xs font-bold">𝕏</span>
                </div>
                <span className="font-medium">Twitter</span>
                <ExternalLink className="w-4 h-4 ml-auto text-muted-foreground" />
              </a>
              <a
                href={user.socialMedia.github}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div className="w-8 h-8 bg-gray-900 rounded flex items-center justify-center">
                  <span className="text-white text-xs font-bold">GH</span>
                </div>
                <span className="font-medium">GitHub</span>
                <ExternalLink className="w-4 h-4 ml-auto text-muted-foreground" />
              </a>
              <a
                href={user.socialMedia.website}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div className="w-8 h-8 bg-green-600 rounded flex items-center justify-center">
                  <span className="text-white text-xs font-bold">🌐</span>
                </div>
                <span className="font-medium">Website</span>
                <ExternalLink className="w-4 h-4 ml-auto text-muted-foreground" />
              </a>
            </CardContent>
          </Card>

          {/* Account Information */}
          <Card>
            <CardHeader>
              <CardTitle>Account Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm text-muted-foreground">Member Since</p>
                <p className="font-medium">{formatDate(user.createdAt)}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Last Updated</p>
                <p className="font-medium">{formatDate(user.updatedAt)}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">User ID</p>
                <p className="font-mono text-sm bg-gray-100 p-2 rounded">
                  {user._id}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
